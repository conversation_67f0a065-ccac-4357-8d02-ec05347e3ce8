package com.ruoyi.platform.service.impl;

import static com.ruoyi.platform.utils.taskUtils.PlatformVideoTaskStatus.*;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.net.URI;
import java.net.URL;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.file.utils.FileOperateUtils;
import com.ruoyi.platform.domain.PlatformAudio;
import com.ruoyi.platform.domain.PlatformImage;
import com.ruoyi.platform.domain.PlatformModel;
import com.ruoyi.platform.domain.PlatformVideo;
import com.ruoyi.platform.domain.vo.DialogueSynthesisRequest;
import com.ruoyi.platform.mapper.PlatformAudioMapper;
import com.ruoyi.platform.mapper.PlatformImageMapper;
import com.ruoyi.platform.mapper.PlatformModelMapper;
import com.ruoyi.platform.mapper.PlatformVideoMapper;
import com.ruoyi.platform.service.IPlatformVideoService;
import com.ruoyi.platform.utils.taskUtils.FileUrlUtils;
import com.ruoyi.platform.utils.taskUtils.HttpClientUtil;
import com.ruoyi.platform.utils.taskUtils.MultipartFileUtils;
import com.ruoyi.platform.utils.taskUtils.PlatformVideoConfig;
import com.ruoyi.platform.utils.taskUtils.PlatformVideoTaskVersion;


/**
 * 视频合成Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-26
 */
@Service
public class PlatformVideoServiceImpl implements IPlatformVideoService 
{
    private static final Logger log = LoggerFactory.getLogger(PlatformVideoServiceImpl.class);

    @Autowired
    private PlatformVideoConfig platformVideoConfig;

    @Autowired
    private PlatformVideoMapper platformVideoMapper;

    @Autowired
    private PlatformModelMapper platformModelMapper;

    @Autowired
    private PlatformAudioMapper platformAudioMapper;

    @Autowired
    private PlatformImageMapper platformImageMapper;

    // 注入阿里云ICE客户端
    @Autowired
    private com.aliyun.ice20201109.Client iceClientAK;

    private static final String PARAM_MODEL = "model";
    private static final String PARAM_MESSAGES = "messages";
    private static final String PARAM_CONTENT = "content"; 

    //查询视频合成
    @Override
    public PlatformVideo selectPlatformVideoById(Long id){
        return platformVideoMapper.selectPlatformVideoById(id);
    }

    // 查询视频合成列表
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<PlatformVideo> selectPlatformVideoList(PlatformVideo platformVideo) {
        List<PlatformVideo> videoList = platformVideoMapper.selectPlatformVideoList(platformVideo);
        return videoList;
    }    

    //修改视频合成
    @Override
    public int updatePlatformVideo(PlatformVideo platformVideo){
        return platformVideoMapper.updatePlatformVideo(platformVideo);
    }

    //批量删除视频合成
    @Override
    public int deletePlatformVideoByIds(Long[] ids){
        for (Long id : ids) {
            PlatformVideo video = selectPlatformVideoById(id);
            if (video != null && StringUtils.isNotEmpty(video.getResultVideo())) {
                if ("2".equals(video.getStatus())) {
                    throw new ServiceException("任务正在处理中，不能删除！");  // 为处理中不让删除
                }
                try {
                    FileOperateUtils.deleteFile(video.getResultVideo()); // 只删除结果视频
                } catch (Exception e) {
                    log.error("删除文件失败", e);
                }
            }
        }
        return platformVideoMapper.deletePlatformVideoByIds(ids);
    }

    // 查询状态为可用的模型
    @Override
    public List<PlatformModel> getAvailableModels() {
        PlatformModel queryModel = new PlatformModel(); 
        queryModel.setModelStatus(1); //0 禁用 1=可用
        return platformModelMapper.selectWyModelList(queryModel);
    }

    //查询待处理和处理中的任务
    @Override
    public List<PlatformVideo> selectPendingTasks() {
        PlatformVideo queryTask = new PlatformVideo();
        queryTask.setStatus(STATUS_PENDING);  
        List<PlatformVideo> pendingTasks = platformVideoMapper.selectPlatformVideoList(queryTask);
        queryTask.setStatus(STATUS_PROCESSING);
        List<PlatformVideo> processingTasks = platformVideoMapper.selectPlatformVideoList(queryTask);
        pendingTasks.addAll(processingTasks);
        return pendingTasks;
    }


    //创建一条视频合成任务
    @Override
    public Long add(PlatformVideo platformVideo) {
        platformVideo.setNumber(FileUrlUtils.generateVideoName()); //任务编号
        platformVideo.setCreateBy(SecurityUtils.getUsername()); //创建人
        platformVideo.setUpdateBy(SecurityUtils.getUsername()); //修改人
        platformVideo.setCreatedAt(DateUtils.getNowDate()); //创建时间
        platformVideo.setUpdatedAt(DateUtils.getNowDate()); //修改时间
        platformVideo.setVersion(PlatformVideoTaskVersion.M_VERSION); // 设置版本号
        platformVideoMapper.insertPlatformVideo(platformVideo);
        Long taskId = platformVideo.getId();
        return taskId;
    }

    //查询视频合成单个任务
    @Override
    public PlatformVideo getOneTask(String version) {
        PlatformVideo condition = new PlatformVideo();
        condition.setStatus( "1"); // 1待处理 2处理中 3成功 4失败',
        condition.setVersion(version); // 版本号
        PlatformVideo musetalkTask = platformVideoMapper.selectPlatformVideoOne(condition);
        if (musetalkTask == null) {
            log.info("没有待处理M版合成任务");
            return null;
        }
        log.info("获取到待处理任务，任务编号为：{}", musetalkTask.getNumber());
        musetalkTask.setStatus("2"); // 改为 处理中
        platformVideoMapper.updatePlatformVideo(musetalkTask);
        return musetalkTask;
    }

    //根据版本查询视频合成单个任务
    @Override
    public PlatformVideo getOneTaskByVersion(String version) {
        PlatformVideo condition = new PlatformVideo();
        condition.setStatus( "1"); // 1待处理 2处理中 3成功 4失败',
        condition.setVersion(version); // 版本号
        PlatformVideo task = platformVideoMapper.selectPlatformVideoOne(condition);
        if (task == null) {
            log.info("没有待处理"+version+"版合成任务");
            return null;
        }
        log.info("获取到待处理任务，任务编号为：{}", task.getNumber());
        task.setStatus("2"); // 改为 处理中
        platformVideoMapper.updatePlatformVideo(task);
        return task;
    }

    //根据动态任务结果调整任务的状态
    @Override
    public void updateTaskStatus(Long id, Long status, String resultVideo) {
        PlatformVideo platformVideo = new PlatformVideo();
        platformVideo.setId(id);
        platformVideo.setStatus(String.valueOf(status));
        platformVideo.setResultVideo(resultVideo);
        if (status == 4) {
            JSONObject operation = new JSONObject(); // 处理失败时的错误信息 - 存入JSON
            operation.put("errorResult", "Task processing failed"); 
            platformVideo.setOperation(operation.toString());
        }
        if (status == 3) {
            platformVideo.setCompleteAt(DateUtils.getNowDate()); //设置视频合成的完成时间
        }
        platformVideoMapper.updatePlatformVideo(platformVideo);
    }

    //上传音频文件
    @Override
    public Object uploadAudioFile(MultipartFile file){
        try {
            if (file == null || file.isEmpty()) {
                throw new ServiceException("音频素材不能为空！");
            }
            long maxFileSize = 100 * 1024 * 1024; // 100MB
            if (file.getSize() > maxFileSize) {
                throw new ServiceException("音频素材文件不能超过100MB！");
            }
            String documentFileName = file.getOriginalFilename();
            String filePath = "video/audio"; // 音频素材文件存储位置
            long timestamp = System.currentTimeMillis();
            String uniqueFileName = timestamp + "_" + documentFileName;
            String fullPath = filePath + "/" + uniqueFileName;
            fullPath = FileOperateUtils.upload(fullPath, file, null);
            String md5 = Md5Utils.getMd5(file);
            Map<String, String> result = new HashMap<>();
            result.put("url", fullPath);
            result.put("md5", md5);
            JSONObject operation = new JSONObject();
            operation.put("driven_audio_md5", md5);
            result.put("operation", operation.toString());
            return result; 
        } catch (IOException e) {
            throw new ServiceException("上传音频文件失败: " + e.getMessage()); // 返回失败响应queryVideoTask
        }
    }

    //查询视频合成任务(Map参数版本) v版
    @SuppressWarnings("unchecked")
    public PlatformVideo queryVideoTask(String model, Long taskId, Map<String, Object> params) {
        if (params != null) {
            if (!params.containsKey("messages") || !params.containsKey("model")) {
                throw new ServiceException("请求参数不完整");
            }
            List<Map<String, Object>> messages = (List<Map<String, Object>>) params.get("messages");
            if (messages == null || messages.isEmpty()) {
                throw new ServiceException("messages不能为空");
            }
            Map<String, Object> content = (Map<String, Object>) messages.get(0).get("content");
            if (content == null || !content.containsKey("task_id")) {
                throw new ServiceException("task_id不能为空");
            }
            model = params.get("model").toString();
            taskId = Long.valueOf(content.get("task_id").toString());
        }
        PlatformVideo task = selectPlatformVideoById(taskId);
        if (task == null) {
            throw new ServiceException("任务不存在");
        }
        return task;
    }

    //上传媒体文件(视频或音频)
    @Override
    public Map<String, String> uploadMedia(MultipartFile file, String type) throws Exception {
        if (file == null || file.isEmpty()) {
            throw new ServiceException("文件不能为空");
        }
        if (file.getSize() > 500 * 1024 * 1024) {
            throw new ServiceException("文件不能超过500MB");
        }
        // 根据类型选择不同的存储基础路径
        String basePath;
        if ("video".equals(type)) {
            basePath = "video/result";
        } else if ("audio".equals(type)) {
            basePath = "video/audio";
        } else {
            basePath = "video/other";
        }
        String currentDate = new SimpleDateFormat("yyyy/MM/dd").format(new Date());

        // 使用时间戳的后6位作为前缀
        String timestamp = String.valueOf(System.currentTimeMillis());
        String shortTimestamp = timestamp.substring(Math.max(0, timestamp.length() - 6)); // 取后六位
        shortTimestamp = String.format("%06d", Long.parseLong(shortTimestamp)); // 补零保证6位
        String originalFilename = file.getOriginalFilename();
        String newFileName = shortTimestamp + "_" + originalFilename;
        String fullPath = String.format("%s/%s/%s", basePath, currentDate, newFileName);
        String savedPath = FileOperateUtils.upload(fullPath, file, null);
        // 构建结果
        Map<String, String> result = new HashMap<>();
        result.put("savedPath", fullPath);     // 真实存储路径，用于数据库存储
        result.put("url", savedPath);          // 临时访问URL，用于前端展示
        result.put("type", type);               // 文件类型
        return result;
    }

    //创建数字人视频合成任务 v版
    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> createVideoSynthesisWithUrls(Map<String, Object> params) throws Exception {
        Map<String, Object> originalParams = JSON.parseObject(JSON.toJSONString(params));
        if (!params.containsKey(PARAM_MODEL)) {
            throw new ServiceException("缺少模型编码");
        }
        String modelCode = params.get(PARAM_MODEL).toString();
        PlatformModel modelInfo = platformModelMapper.selectWyModelByCode(modelCode);
        if (modelInfo == null || !modelInfo.getModelStatus().equals(1)) {
            throw new ServiceException("模型不可用");
        }
        List<Map<String, Object>> messages = (List<Map<String, Object>>) params.get(PARAM_MESSAGES);
        if (messages != null && !messages.isEmpty()) {
            Map<String, Object> content = (Map<String, Object>) messages.get(0).get(PARAM_CONTENT);
            if (content != null) {
                if (content.containsKey("live_video_url")) {
                    String videoPath = content.get("live_video_url").toString();
                    String originalPath = FileUrlUtils.extractStoragePath(videoPath);
                    if (StringUtils.isEmpty(originalPath)) {
                        throw new ServiceException("视频路径无效");
                    }
                    String tempUrl = FileOperateUtils.getURL(originalPath);
                    if (tempUrl != null) {
                        content.put("live_video_url", tempUrl);
                    }
                }
                if (content.containsKey("live_sound_url")) {
                    String audioPath = content.get("live_sound_url").toString();
                    String originalPath = FileUrlUtils.extractStoragePath(audioPath);
                    String tempUrl = FileOperateUtils.getURL(originalPath);
                    if (tempUrl != null) {
                        content.put("live_sound_url", tempUrl);
                    }
                }
            }
        }
        Map<String, Object> result = createVideoSynthesis(params);
        saveVideoTask(originalParams, modelCode, result);
        return result;
    }

    @SuppressWarnings("unchecked")
    private void saveVideoTask(Map<String, Object> params, String modelCode, Map<String, Object> result) {
        try {
            PlatformVideo task = new PlatformVideo();
            task.setModel(modelCode);
            task.setTaskNo(result.get("task_id").toString());
            task.setNumber(FileUrlUtils.generateVideoName());
            task.setVersion("V");
            task.setStatus(result.getOrDefault("status", STATUS_PENDING).toString());

            String username = SecurityUtils.getUsername();
            Date nowDate = DateUtils.getNowDate();
            task.setCreateBy(username);
            task.setUpdateBy(username);
            task.setCreatedAt(nowDate);
            task.setUpdatedAt(nowDate);

            if (params.containsKey(PARAM_MESSAGES)) {
                List<Map<String, Object>> messages = (List<Map<String, Object>>) params.get(PARAM_MESSAGES);
                if (!messages.isEmpty()) {
                    Map<String, Object> content = (Map<String, Object>) messages.get(0).get(PARAM_CONTENT);
                    if (content != null) {
                        if (content.containsKey("live_video_url")) {
                            String videoPath = content.get("live_video_url").toString();
                            String originalPath = FileUrlUtils.extractStoragePath(videoPath);
                            task.setDrivenVideo(originalPath);
                        }
                        if (content.containsKey("live_sound_url")) {
                            String audioPath = content.get("live_sound_url").toString();
                            String originalPath = FileUrlUtils.extractStoragePath(audioPath);
                            task.setDrivenAudio(originalPath);
                        }
                        task.setCallbackUrl(content.getOrDefault("callback_url", "").toString());
                    }
                }
            }
            PlatformModel modelInfo = platformModelMapper.selectWyModelByCode(modelCode);
            JSONObject jsonBuilder = new JSONObject();
            jsonBuilder.put("video_priority", "0");
            jsonBuilder.put("code", result.getOrDefault("code", "200"));
            jsonBuilder.put("video_message", result.getOrDefault("msg", "任务创建成功"));
            if (modelInfo != null && StringUtils.isNotEmpty(modelInfo.getModelVersion())) {
                jsonBuilder.put("model_price", modelInfo.getModelVersion());
            }
            String jsonStr = jsonBuilder.toJSONString();
            task.setOperation(jsonStr);
            int rows = platformVideoMapper.insertPlatformVideo(task);
            if (rows <= 0) {
                throw new ServiceException("保存任务记录失败");
            }
        } catch (Exception e) {
            throw new ServiceException("保存任务记录失败: " + e.getMessage());
        }
    }

    // 从临时凭证URL下载视频并重新上传到对象存储
    public static String downloadAndSaveVideo(String temporaryUrl) throws Exception {
        String tempFileName = UUID.randomUUID().toString() + ".mp4"; // 创建临时文件
        String tempFilePath = System.getProperty("java.io.tmpdir") + File.separator + tempFileName;
        File tempFile = new File(tempFilePath);
        try {
            URL url = new URI(temporaryUrl).toURL();
            URLConnection conn = url.openConnection();
            conn.setConnectTimeout(30000);  // 设置30秒连接超时
            conn.setReadTimeout(120000);    // 设置120秒读取超时
            try (InputStream inputStream = conn.getInputStream();
                 FileOutputStream outputStream = new FileOutputStream(tempFile)) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }
            String currentDate = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
            String timestamp = String.valueOf(System.currentTimeMillis());
            String fileName = timestamp + ".mp4";
            String address = "video/result";
            String fullPath = String.format("%s/%s/%s", address, currentDate, fileName);
            MultipartFile multipartFile = MultipartFileUtils.createFromFile(
                tempFile, "file", fileName, "video/mp4");
            String savedPath = FileOperateUtils.upload(fullPath, multipartFile);
            return savedPath;
        } catch (Exception e) {
            throw e;
        } finally {
            if (tempFile.exists()) {
                if (!tempFile.delete()) {
                    log.warn("临时文件删除失败: {}", tempFilePath); // 清理临时文件
                }
            }
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> createVideoSynthesis(Map<String, Object> params) {
        if (platformVideoConfig == null || StringUtils.isEmpty(platformVideoConfig.getServerUrl())) {
            throw new RuntimeException("视频合成配置不完整");  // 配置检查
        }
        String modelCode = String.valueOf(params.get(PARAM_MODEL));
        PlatformModel modelInfo = platformModelMapper.selectWyModelByCode(modelCode); // 获取并记录模型价位信息
        String url = platformVideoConfig.getServerUrl().trim();
        if (!url.endsWith("/")) {
            url += "/";  // 构建完整URL - 确保正确拼接
        }
        url += platformVideoConfig.getSynthesisPath().trim().replaceFirst("^/", "");
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + platformVideoConfig.getApiKey());
        headers.put("Content-Type", "application/json");
        headers.put("Accept", "application/json");
        try {
            String jsonBody = JSON.toJSONString(params);
            String response = HttpClientUtil.doPost(url, jsonBody, headers);
            Map<String, Object> apiResponse = JSON.parseObject(response);
            Map<String, Object> result = new HashMap<>();
            if (apiResponse != null) {
                result.put("code", apiResponse.getOrDefault("code", 500));
                result.put("msg", apiResponse.getOrDefault("msg", "未知错误"));
                Object dataObj = apiResponse.get("data"); // 获取data
                if (dataObj instanceof Map) {
                    Map<String, Object> data = (Map<String, Object>) dataObj;
                    Object taskId = data.get("task_id");  // 获取task_id
                    result.put("task_id", taskId != null ? taskId.toString() : "");
                    result.put("status", STATUS_PENDING);
                    // 添加模型价位信息到结果中
                    if (modelInfo != null && StringUtils.isNotEmpty(modelInfo.getModelVersion())) {
                        result.put("model_price", modelInfo.getModelVersion());
                    }
                } else {
                    result.put("task_id", "");
                    result.put("status", STATUS_FAILED);
                }
            } else {
                result.put("code", 500);
                result.put("msg", "API响应为空");
                result.put("task_id", "");
                result.put("status", STATUS_FAILED);
            }
            return result;
        } catch (Exception e) {
            throw new ServiceException("创建视频合成任务失败: " + e.getMessage());
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> queryVideoSynthesis(String taskNo) {
        if (StringUtils.isEmpty(platformVideoConfig.getQueryPath())) {
            throw new ServiceException("查询接口路径未配置");
        }
        String url = platformVideoConfig.getServerUrl() + platformVideoConfig.getQueryPath();
        try {
            Map<String, Object> params = new HashMap<>();  // 构造查询参数
            params.put("model", "umi_video_v5");

            Map<String, Object> content = new HashMap<>();
            content.put("type", 2);
            content.put("task_id", taskNo);

            List<Map<String, Object>> messages = new ArrayList<>();
            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", content);
            messages.add(message);
            params.put("messages", messages);

            String jsonBody = JSON.toJSONString(params);
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", "Bearer " + platformVideoConfig.getApiKey());
            headers.put("Content-Type", "application/json");
            headers.put("Accept", "application/json");
            String response = HttpClientUtil.doPost(url, jsonBody, headers);  // 发送POST请求
            Map<String, Object> apiResponse = JSON.parseObject(response);
            Map<String, Object> result = new HashMap<>();  // 构造标准响应
            result.put("taskNo", taskNo);
            result.put("code", apiResponse.getOrDefault("code", 500));
            result.put("message", apiResponse.getOrDefault("msg", "未知错误"));
            
            Map<String, Object> data = (Map<String, Object>) apiResponse.get("data"); // 处理data部分
            if (data != null) {
                Integer status = (Integer) data.get("status");
                PlatformVideo queryTask = new PlatformVideo(); // 查询任务信息
                queryTask.setTaskNo(taskNo);
                List<PlatformVideo> tasks = selectPlatformVideoList(queryTask);
                PlatformVideo task = !tasks.isEmpty() ? tasks.get(0) : null;
                
                // 如果任务存在，添加模型价位信息到结果中
                if (task != null && StringUtils.isNotEmpty(task.getOperation())) {
                    try {
                        JSONObject operationJson = JSONObject.parseObject(task.getOperation());
                        if (operationJson.containsKey("model_price")) {
                            String modelPrice = operationJson.getString("model_price");
                            result.put("modelPrice", modelPrice);
                        } else {
                            log.warn("任务[{}]的操作JSON中未找到model_price字段", task.getId());
                        }
                    } catch (Exception e) {
                        log.warn("解析操作JSON失败: {}", e.getMessage());
                    }
                }
                switch (status) {
                    case 1:
                        result.put("status", STATUS_PENDING);
                        result.put("message", STATUS_DESC_PENDING);
                        break;
                    case 2:
                        result.put("status", STATUS_PROCESSING);
                        result.put("message", STATUS_DESC_PROCESSING);
                        break;
                    case 3: // 成功状态
                        String videoUrl = data.get("complete_url").toString();
                        try {
                            if (task != null && StringUtils.isEmpty(task.getResultVideo())) {
                                // 下载并保存视频，返回的是相对路径
                                String savedPath = downloadAndSaveVideo(videoUrl);
                                
                                result.put("status", STATUS_SUCCESS);
                                result.put("message", STATUS_DESC_SUCCESS);
                                result.put("originalStoragePath", savedPath);
                                
                                PlatformVideo updateTask = new PlatformVideo();
                                updateTask.setId(task.getId());
                                updateTask.setStatus(STATUS_SUCCESS);
                                updateTask.setVideoMessage(STATUS_DESC_SUCCESS);
                                // 关键修改：确保存储的是相对路径，不是临时URL
                                updateTask.setResultVideo(savedPath); // 这里必须是相对路径
                                Date nowDate = new Date();
                                updateTask.setCompleteAt(nowDate);
                                updateTask.setUpdatedAt(nowDate);
                                updateTask.setUpdateBy(SecurityUtils.getUsername());
                                platformVideoMapper.updatePlatformVideo(updateTask);
                                
                                // 生成临时访问URL仅供前端显示使用
                                String tempUrl = FileOperateUtils.getURL(savedPath);
                                result.put("videoUrl", tempUrl);
                                result.put("realVideoUrl", savedPath); // 真实的相对路径
                            } else if (task != null && StringUtils.isNotEmpty(task.getResultVideo())) {
                                // 任务已处理过，直接生成临时访问URL
                                result.put("status", STATUS_SUCCESS);
                                result.put("message", STATUS_DESC_SUCCESS);
                                result.put("originalStoragePath", task.getResultVideo());
                                
                                // 生成临时访问URL供前端显示
                                String tempUrl = FileOperateUtils.getURL(task.getResultVideo());
                                result.put("videoUrl", tempUrl);
                            }
                        } catch (Exception e) {
                            log.error("处理视频URL失败");
                        }
                        break;
                    case 4:
                        result.put("status", STATUS_FAILED);
                        result.put("message", STATUS_DESC_FAILED);
                        if (task != null) {
                            task.setStatus(STATUS_FAILED);
                            task.setVideoMessage(STATUS_DESC_FAILED);
                            task.setUpdateTime(new Date());
                            task.setUpdateBy(SecurityUtils.getUsername());
                            platformVideoMapper.updatePlatformVideo(task);
                        } else {
                            log.warn("无法找到任务ID为 {} 的记录", taskNo);
                        }
                        break;
                    default:
                        throw new ServiceException("未知状态码: " + status);
                }
                if (task != null) {
                    result.put("taskId", task.getId()); // 添加其他任务信息
                    result.put("model", task.getModel());
                    result.put("videoName", task.getNumber());
                    // 处理源文件URL
                    if (StringUtils.isNotEmpty(task.getDrivenVideo())) {
                        String videoTempUrl = FileOperateUtils.getURL(task.getDrivenVideo());
                        result.put("liveVideoUrl", videoTempUrl.toString());
                    }
                    if (StringUtils.isNotEmpty(task.getDrivenAudio())) {
                        String audioTempUrl = FileOperateUtils.getURL(task.getDrivenAudio());
                        result.put("liveSoundUrl", audioTempUrl.toString());
                    }
                    result.put("createTime", task.getCreateTime());
                    result.put("updateTime", task.getUpdateTime());
                }
            }
            return result;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 创建任务  合成 H 版视频
     */
    @Override
    public Long synthesisH(PlatformVideo platformVideo) {
        platformVideo.setNumber(FileUrlUtils.generateVideoName()); //任务编号
        platformVideo.setCreateBy(SecurityUtils.getUsername()); //创建人
        platformVideo.setUpdateBy(SecurityUtils.getUsername()); //修改人
        platformVideo.setCreatedAt(DateUtils.getNowDate()); //创建时间
        platformVideo.setUpdatedAt(DateUtils.getNowDate()); //修改时间
        platformVideo.setVersion(PlatformVideoTaskVersion.H_VERSION); // 设置版本号
        platformVideoMapper.insertPlatformVideo(platformVideo);
        Long taskId = platformVideo.getId();
        return taskId;

    }

    /**
     * 数字人对话合成
     */
    @Override
    public Map<String, Object> createDialogueSynthesis(DialogueSynthesisRequest request) {
        validateDialogueRequest(request);

        // 生成对话组ID
        String dialogueGroupId = "dialogue_" + System.currentTimeMillis();

        List<Long> videoTaskIds = request.getDialogueContent().stream().map(dialogue -> {
            // 查找对应的数字人配置
            DialogueSynthesisRequest.DigitalHuman human = request.getDigitalHumans().stream()
                .filter(h -> dialogue.getSpeaker().equals(h.getId())).findFirst()
                .orElseThrow(() -> new ServiceException("找不到发言人配置: " + dialogue.getSpeakerName()));
            Map<String, String> audioResult = generateAudioForText(dialogue.getText(), human); // 生成音频

            // 创建视频任务，并添加对话组ID和顺序信息
            return createVideoTask(audioResult.get("audioUrl"), audioResult.get("audioMd5"),
                human, dialogue, request.getVersion(), request.getBboxShiftValue(), request.getModel(), dialogueGroupId);
        }).collect(Collectors.toList());

        // 返回结果包含任务ID列表和对话组ID
        Map<String, Object> result = new HashMap<>();
        result.put("videoTaskIds", videoTaskIds);
        result.put("dialogueGroupId", dialogueGroupId);
        result.put("message", "数字人对话合成任务已创建");

        return result;
    }

    /**
     * 验证对话合成请求参数
     */
    private void validateDialogueRequest(DialogueSynthesisRequest request) {
        if (request.getDigitalHumans() == null || request.getDigitalHumans().isEmpty()) {
            throw new ServiceException("数字人配置不能为空");
        }
        if (request.getDialogueContent() == null || request.getDialogueContent().isEmpty()) {
            throw new ServiceException("对话内容不能为空");
        }
        String version = request.getVersion();
        if (!"M".equalsIgnoreCase(version) && !"H".equalsIgnoreCase(version) && !"V".equalsIgnoreCase(version)) {
            throw new ServiceException("不支持的版本类型: " + version);
        }
        // 数字人配置验证
        String[] supportedVoices = {"zhiyuan", "zhiyue", "zhisha", "zhida", "aiqi", "aicheng", "aijia","siqi", "sijia", "mashu", "yueer", "ruoxi", "aida", "sicheng","ninger", "xiaoyun", "xiaogang", "ruilin"};
        for (DialogueSynthesisRequest.DigitalHuman human : request.getDigitalHumans()) {
            if (StringUtils.isEmpty(human.getAvatarAddress()) || StringUtils.isEmpty(human.getVoiceName())) {
                throw new ServiceException("数字人配置不完整");
            }
            if ("system".equals(human.getVoiceType()) && human.getVoiceId() == null) {
                throw new ServiceException("系统声音ID不能为空");
            }
            if ("builtin".equals(human.getVoiceType())) {
                boolean isSupported = false;
                for (String voice : supportedVoices) {
                    if (voice.equals(human.getVoiceName())) {
                        isSupported = true;
                        break;
                    }
                }
                if (!isSupported) {
                    throw new ServiceException("不支持的内置音色: " + human.getVoiceName());
                }
            }
        }
        if ("M".equalsIgnoreCase(version) && request.getBboxShiftValue() != null) {
            int bboxValue = request.getBboxShiftValue();
            if (bboxValue < -7 || bboxValue > 7) {
                throw new ServiceException("M版边界框偏移值必须在-7到+7之间");
            }
        }
        if ("V".equalsIgnoreCase(version)) {
            if (StringUtils.isEmpty(request.getModel())) {
                throw new ServiceException("V版必须指定模型编码");
            }
            List<PlatformModel> models = getAvailableModels();
            boolean modelExists = models.stream().anyMatch(model -> request.getModel().equals(model.getModelCode()));
            if (!modelExists) {
                throw new ServiceException("模型不存在或不可用: " + request.getModel());
            }
        }
    }

    /**
     * 为单个文本生成音频
     */
    private Map<String, String> generateAudioForText(String text, DialogueSynthesisRequest.DigitalHuman human) {
        try {
            return switch (human.getVoiceType()) {
                case "system" -> generateSystemAudio(text, human.getVoiceId());
                case "builtin" -> generateBuiltinAudio(text, human.getVoiceName());
                default -> throw new ServiceException("不支持的声音类型: " + human.getVoiceType());
            };
        } catch (Exception e) {
            log.error("音频生成失败 - 文本: {}, 声音类型: {}, 错误: {}",
                text.substring(0, Math.min(20, text.length())), human.getVoiceType(), e.getMessage());
            throw new ServiceException("音频生成失败: " + e.getMessage());
        }
    }

    /**
     * 创建视频合成任务
     */
    private Long createVideoTask(String audioUrl, String audioMd5, DialogueSynthesisRequest.DigitalHuman human,
    DialogueSynthesisRequest.DialogueContent dialogue, String version, Integer bboxShiftValue, String modelCode, String dialogueGroupId) {
        if ("V".equalsIgnoreCase(version)) {
            return createVVersionTask(audioUrl, audioMd5, human, dialogue, modelCode, dialogueGroupId);
        }
        PlatformVideo videoTask = buildVideoTask(audioUrl, human, dialogue, audioMd5, version, bboxShiftValue, dialogueGroupId);
        return "M".equalsIgnoreCase(version) ? add(videoTask) : synthesisH(videoTask);
    }

    /**
     * 构建视频任务对象
     */
    private PlatformVideo buildVideoTask(String audioUrl, DialogueSynthesisRequest.DigitalHuman human,
        DialogueSynthesisRequest.DialogueContent dialogue, String audioMd5, String version, Integer bboxShiftValue, String dialogueGroupId) {
        PlatformVideo videoTask = new PlatformVideo();
        videoTask.setDrivenAudio(audioUrl);
        videoTask.setDrivenVideo(human.getAvatarAddress());
        videoTask.setStatus("1");
        JSONObject operation = buildBaseOperationJson(human, dialogue, audioMd5, version);
        if ("M".equalsIgnoreCase(version) && bboxShiftValue != null) {
            operation.put("bbox_shift_value", bboxShiftValue);
        }
        // 添加对话组ID和顺序信息
        if (StringUtils.isNotEmpty(dialogueGroupId)) {
            operation.put("dialogueGroupId", dialogueGroupId);
            operation.put("dialogueOrder", dialogue.getOrder() != null ? dialogue.getOrder() : 0);
        }
        videoTask.setOperationJson(operation);
        return videoTask;
    }

    /**
     * 创建V版视频合成任务
     */
    private Long createVVersionTask(String audioUrl, String audioMd5, DialogueSynthesisRequest.DigitalHuman human,
        DialogueSynthesisRequest.DialogueContent dialogue, String modelCode, String dialogueGroupId) {
        try {
            String videoName = FileUrlUtils.generateVideoName();
            Map<String, Object> params = new HashMap<>();
            params.put(PARAM_MODEL, modelCode);
            Map<String, Object> content = new HashMap<>();
            content.put("type", 1);
            content.put("video_name", videoName);
            try {
                String audioTempUrl = FileOperateUtils.getURL(audioUrl);
                String videoTempUrl = FileOperateUtils.getURL(human.getAvatarAddress());
                content.put("live_sound_url", audioTempUrl != null ? audioTempUrl : audioUrl);
                content.put("live_video_url", videoTempUrl != null ? videoTempUrl : human.getAvatarAddress());
            } catch (Exception e) {
                log.warn("获取临时URL失败，使用原始URL");
                content.put("live_sound_url", audioUrl);
                content.put("live_video_url", human.getAvatarAddress());
            }
            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", content);
            List<Map<String, Object>> messages = new ArrayList<>();
            messages.add(message);
            params.put("messages", messages);
            Map<String, Object> apiResult = createVideoSynthesis(params);// 调用V版创建任务接口
            if (apiResult.containsKey("code")) {
                Object code = apiResult.get("code");
                if (!"200".equals(String.valueOf(code)) && !Integer.valueOf(200).equals(code)) {
                    log.error("V版API调用失败 - code: {}, msg: {}", code, apiResult.get("msg"));
                }
            }
            return saveVVersionTask(modelCode, apiResult, audioUrl, audioMd5, human, dialogue, videoName, dialogueGroupId);
        } catch (Exception e) {
            throw new ServiceException("创建V版视频任务失败: " + e.getMessage());
        }
    }

    /**
     * 保存V版任务到数据库
     */
    private Long saveVVersionTask(String modelCode, Map<String, Object> apiResult, String audioUrl, String audioMd5,
        DialogueSynthesisRequest.DigitalHuman human, DialogueSynthesisRequest.DialogueContent dialogue, String videoName, String dialogueGroupId) {
        try {
            String taskId = "";
            if (apiResult.containsKey("task_id")) {
                taskId = apiResult.get("task_id").toString();
            } else if (apiResult.containsKey("data") && apiResult.get("data") instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> data = (Map<String, Object>) apiResult.get("data");
                if (data.containsKey("task_id")) {
                    taskId = data.get("task_id").toString();
                }
            }
            PlatformVideo task = new PlatformVideo();
            task.setModel(modelCode);
            task.setTaskNo(taskId);
            task.setNumber(videoName);
            task.setVersion(PlatformVideoTaskVersion.V_VERSION);
            task.setStatus(apiResult.getOrDefault("status", STATUS_PENDING).toString());
            task.setDrivenAudio(audioUrl);
            task.setDrivenVideo(human.getAvatarAddress());
            task.setCreateBy(SecurityUtils.getUsername());
            task.setUpdateBy(SecurityUtils.getUsername());
            task.setCreatedAt(DateUtils.getNowDate());
            task.setUpdatedAt(DateUtils.getNowDate());
            JSONObject operation = buildBaseOperationJson(human, dialogue, audioMd5, "V");
            operation.put("video_priority", "0");
            operation.put("code", apiResult.getOrDefault("code", "200"));
            operation.put("video_message", apiResult.getOrDefault("msg", "任务创建成功"));
            // 添加对话组ID和顺序信息
            if (StringUtils.isNotEmpty(dialogueGroupId)) {
                operation.put("dialogueGroupId", dialogueGroupId);
                operation.put("dialogueOrder", dialogue.getOrder() != null ? dialogue.getOrder() : 0);
            }
            PlatformModel modelInfo = platformModelMapper.selectWyModelByCode(modelCode);
            if (modelInfo != null && StringUtils.isNotEmpty(modelInfo.getModelVersion())) {
                operation.put("model_price", modelInfo.getModelVersion());
            }
            task.setOperation(operation.toJSONString());
            int rows = platformVideoMapper.insertPlatformVideo(task);
            if (rows <= 0) {
                throw new ServiceException("保存V版任务记录失败");
            }
            return task.getId();
        } catch (Exception e) {
            throw new ServiceException("保存V版任务记录失败: " + e.getMessage());
        }
    }

    /**
     * 使用数智宝系统声音生成音频
     */
    private Map<String, String> generateSystemAudio(String text, Long voiceId) {
        try {
            Object platformTaskService = SpringUtils.getBean("platformTaskServiceImpl");
            if (platformTaskService == null) {
                throw new ServiceException("无法获取PlatformTaskService服务");
            }
            // 调用同步方法，返回音频路径
            Method syncMethod = platformTaskService.getClass().getMethod("createTextToAudioSync", String.class, Long.class, Long.class);
            String audioPath = (String) syncMethod.invoke(platformTaskService, text, voiceId, null);

            if (StringUtils.isEmpty(audioPath)) {
                throw new ServiceException("系统音频合成失败");
            }
            String audioMd5 = FileOperateUtils.getMd5ForFilePath(audioPath);
            if (StringUtils.isEmpty(audioMd5)) {
                PlatformAudio audio = platformAudioMapper.getAudioDetailByAddress(audioPath);
                if (audio != null) {
                    audioMd5 = audio.getAudioMd5();
                }
            }
            Map<String, String> result = new HashMap<>();
            result.put("audioUrl", audioPath);
            result.put("audioMd5", audioMd5);
            return result;
        } catch (Exception e) {
            throw new ServiceException("系统音频合成失败: " + e.getMessage());
        }
    }

    /**
     * 使用内置音色生成音频（阿里云）
     */
    private Map<String, String> generateBuiltinAudio(String text, String voiceName) {
        try {
            Object platformTaskService = SpringUtils.getBean("platformTaskServiceImpl");
            Object ttsRequest = createTtsRequest(text, voiceName);
            Method method = platformTaskService.getClass().getMethod("synthesize", ttsRequest.getClass());
            String audioPath = (String) method.invoke(platformTaskService, ttsRequest);
            if (StringUtils.isEmpty(audioPath)) {
                throw new ServiceException("内置音色合成失败");
            }
            String audioMd5 = FileOperateUtils.getMd5ForFilePath(audioPath);
            if (StringUtils.isEmpty(audioMd5)) {
                PlatformAudio audio = platformAudioMapper.getAudioDetailByAddress(audioPath);
                if (audio != null) {
                    audioMd5 = audio.getAudioMd5();
                }
            }
            Map<String, String> result = new HashMap<>();
            result.put("audioUrl", audioPath);
            result.put("audioMd5", audioMd5);
            return result;
        } catch (Exception e) {
            throw new ServiceException("内置音色合成失败: " + e.getMessage());
        }
    }

    /**
     * 创建TtsRequest对象
     */
    private Object createTtsRequest(String text, String voiceName) {
        try {
            Class<?> ttsRequestClass = Class.forName("com.ruoyi.platform.model.domain.TtsRequest");
            Object ttsRequest = ttsRequestClass.getDeclaredConstructor().newInstance();
            setTtsProperty(ttsRequest, "setText", text);
            setTtsProperty(ttsRequest, "setVoice", voiceName != null ? voiceName : "zhiyuan");
            setTtsProperty(ttsRequest, "setFormat", "wav");
            setTtsProperty(ttsRequest, "setSampleRate", 16000);
            setTtsProperty(ttsRequest, "setVolume", 50);
            return ttsRequest;
        } catch (Exception e) {
            throw new ServiceException("创建TtsRequest对象失败: " + e.getMessage());
        }
    }

    /**
     * 设置TTS对象属性
     */
    private void setTtsProperty(Object ttsRequest, String methodName, Object value) throws Exception {
        Class<?> paramType = value instanceof Integer ? int.class : String.class;
        java.lang.reflect.Method method = ttsRequest.getClass().getMethod(methodName, paramType);
        method.invoke(ttsRequest, value);
    }

    /**
     * 构建基础操作信息JSON
     */
    private JSONObject buildBaseOperationJson(DialogueSynthesisRequest.DigitalHuman human,
        DialogueSynthesisRequest.DialogueContent dialogue, String audioMd5, String version) {
        JSONObject operation = new JSONObject();
        operation.put("dialogue_id", dialogue.getId());
        operation.put("speaker_id", dialogue.getSpeaker());
        operation.put("speaker_name", dialogue.getSpeakerName());
        operation.put("text", dialogue.getText());
        operation.put("avatar_name", human.getAvatarName());
        operation.put("voice_name", human.getVoiceName());
        operation.put("voice_type", human.getVoiceType());
        operation.put("task_type", "dialogue_synthesis_item");
        operation.put("driven_audio_md5", audioMd5);
        operation.put("driven_video_md5", getAvatarMd5(human.getAvatarAddress()));
        operation.put("model_price", "V".equalsIgnoreCase(version) ? "600" : "500");
        return operation;
    }

    /**
     * 获取形象MD5值 - 直接查询数据库
     */
    private String getAvatarMd5(String avatarAddress) {
        if (StringUtils.isEmpty(avatarAddress)) {
            return null;
        }
        try {
            Map<String, Object> condition = Map.of("imageAddress", avatarAddress);
            List<PlatformImage> images = platformImageMapper.selectImagesByCondition(condition);
            return images != null && !images.isEmpty() ? images.get(0).getMd5() : null;
        } catch (Exception e) {
            return null;
        }
    }



    /**
     * 构建视频片段列表
     */
    private List<Map<String, Object>> buildVideoClips(List<PlatformVideo> videoTasks) {
        List<Map<String, Object>> clips = new ArrayList<>();

        for (int i = 0; i < videoTasks.size(); i++) {
            PlatformVideo task = videoTasks.get(i);
            String videoUrl;
            try {
                videoUrl = FileOperateUtils.getURL(task.getResultVideo());
            } catch (Exception e) {
                throw new ServiceException("无法获取视频URL，任务ID: " + task.getId() + "，错误: " + e.getMessage());
            }

            if (StringUtils.isEmpty(videoUrl)) {
                throw new ServiceException("无法获取视频URL，任务ID: " + task.getId());
            }

            Map<String, Object> clip = new HashMap<>();
            clip.put("taskId", task.getId());
            clip.put("videoUrl", videoUrl);
            clip.put("order", i + 1);
            clip.put("duration", 10.0); // 简化处理，假设每个视频10秒

            // 从任务的operationJson中提取发言人和文本信息
            if (StringUtils.isNotEmpty(task.getOperation())) {
                JSONObject operation = JSONObject.parseObject(task.getOperation());
                if (operation.containsKey("speakerName")) {
                    clip.put("speakerName", operation.getString("speakerName"));
                }
                if (operation.containsKey("text")) {
                    clip.put("text", operation.getString("text"));
                }
            }

            clips.add(clip);
        }

        return clips;
    }

    /**
     * 构建云剪辑Timeline
     */
    private String buildVideoClipTimeline(List<Map<String, Object>> videoClips) {
        JSONObject timeline = new JSONObject();

        // 创建视频轨道
        JSONObject videoTrack = new JSONObject();
        List<JSONObject> videoTrackClips = new ArrayList<>();

        double currentTime = 0.0;

        for (Map<String, Object> clip : videoClips) {
            JSONObject videoClip = new JSONObject();
            videoClip.put("MediaURL", clip.get("videoUrl"));
            videoClip.put("In", 0);
            videoClip.put("Out", clip.get("duration"));
            videoClip.put("TimelineIn", currentTime);
            videoClip.put("TimelineOut", currentTime + (Double) clip.get("duration"));

            videoTrackClips.add(videoClip);
            currentTime += (Double) clip.get("duration");
        }

        videoTrack.put("VideoTrackClips", videoTrackClips);
        timeline.put("VideoTracks", List.of(videoTrack));

        return timeline.toJSONString();
    }

    /**
     * 调用阿里云云剪辑API创建工程
     */
    private Map<String, Object> callIceCreateProject(Map<String, Object> params) {
        try {
            log.info("开始调用阿里云ICE API创建云剪辑工程，参数: {}", params);

            // 创建请求对象
            com.aliyun.ice20201109.models.CreateEditingProjectRequest request = new com.aliyun.ice20201109.models.CreateEditingProjectRequest();

            // 设置请求参数
            if (params.containsKey("Title")) {
                request.setTitle(params.get("Title").toString());
            }
            if (params.containsKey("Timeline")) {
                request.setTimeline(params.get("Timeline").toString());
            }
            if (params.containsKey("Description")) {
                request.setDescription(params.get("Description").toString());
            }

            // 调用阿里云ICE API
            com.aliyun.ice20201109.models.CreateEditingProjectResponse response = iceClientAK.createEditingProject(request);

            Map<String, Object> result = new HashMap<>();
            if (response != null && response.getBody() != null) {
                com.aliyun.ice20201109.models.CreateEditingProjectResponseBody body = response.getBody();

                // 获取ProjectId - 根据阿里云ICE API文档
                String projectId = null;
                try {
                    // 检查响应体是否包含Project对象
                    if (body.getProject() != null) {
                        projectId = body.getProject().getProjectId();
                        log.info("通过Project对象获取ProjectId: {}", projectId);
                    } else {
                        log.warn("响应体中没有Project对象，响应内容: {}", body.toString());
                        // 如果没有Project对象，可能ProjectId直接在响应体中
                        try {
                            java.lang.reflect.Field field = body.getClass().getDeclaredField("projectId");
                            field.setAccessible(true);
                            projectId = (String) field.get(body);
                            log.info("通过反射获取ProjectId: {}", projectId);
                        } catch (Exception e) {
                            log.error("无法获取ProjectId，创建工程可能失败", e);
                            throw new ServiceException("创建云剪辑工程失败：无法获取ProjectId");
                        }
                    }
                } catch (Exception e) {
                    log.error("获取ProjectId时发生异常", e);
                    throw new ServiceException("创建云剪辑工程失败：" + e.getMessage());
                }

                result.put("projectId", projectId);
                result.put("ProjectId", projectId);
                result.put("RequestId", body.getRequestId());
                result.put("message", "云剪辑工程创建成功");

                log.info("云剪辑工程创建成功，ProjectId: {}", projectId);
                log.info("Timeline内容: {}", params.get("Timeline"));
                log.info("响应体信息: {}", body.toString());
            }

            return result;

        } catch (Exception e) {
            log.error("调用云剪辑API创建工程失败", e);
            throw new ServiceException("调用云剪辑API创建工程失败: " + e.getMessage());
        }
    }

    /**
     * 提交导出任务 - 使用真实的阿里云ICE API
     */
    private Map<String, Object> submitExportJob(String projectId) {
        try {
            log.info("开始提交导出任务，ProjectId: {}", projectId);

            if (StringUtils.isEmpty(projectId)) {
                throw new ServiceException("ProjectId不能为空");
            }

            // 创建导出任务请求
            com.aliyun.ice20201109.models.SubmitMediaProducingJobRequest request = new com.aliyun.ice20201109.models.SubmitMediaProducingJobRequest();
            request.setProjectId(projectId);

            // 设置输出媒体配置
            String outputFileName = "dialogue_video_" + System.currentTimeMillis() + ".mp4";
            String outputMediaURL = "oss://szb-pc/dialogue_videos/" + outputFileName;

            // 构建输出媒体配置 - 使用正确的格式
            JSONObject outputMediaConfig = new JSONObject();
            outputMediaConfig.put("MediaURL", outputMediaURL);
            outputMediaConfig.put("Width", 1920);
            outputMediaConfig.put("Height", 1080);
            outputMediaConfig.put("Bitrate", 2000000); // 2Mbps
            outputMediaConfig.put("FrameRate", 25);
            outputMediaConfig.put("Format", "mp4");

            request.setOutputMediaConfig(outputMediaConfig.toJSONString());

            log.info("提交导出任务参数 - ProjectId: {}, OutputMediaConfig: {}", projectId, outputMediaConfig.toJSONString());

            // 调用阿里云ICE API
            com.aliyun.ice20201109.models.SubmitMediaProducingJobResponse response = iceClientAK.submitMediaProducingJob(request);

            Map<String, Object> result = new HashMap<>();
            if (response != null && response.getBody() != null) {
                com.aliyun.ice20201109.models.SubmitMediaProducingJobResponseBody body = response.getBody();

                result.put("jobId", body.getJobId());
                result.put("JobId", body.getJobId());
                result.put("RequestId", body.getRequestId());
                result.put("message", "导出任务提交成功");
                result.put("outputMediaURL", outputMediaURL);

                log.info("导出任务提交成功，ProjectId: {}, JobId: {}, OutputURL: {}", projectId, body.getJobId(), outputMediaURL);
            }

            return result;

        } catch (Exception e) {
            log.error("提交导出任务失败，ProjectId: {}", projectId, e);
            throw new ServiceException("提交导出任务失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> createDialogueVideoClipByGroupId(String dialogueGroupId, String title, String description) {
        if (StringUtils.isEmpty(dialogueGroupId)) {
            throw new ServiceException("对话组ID不能为空");
        }

        try {
            // 根据对话组ID查找对应的已完成任务
            List<PlatformVideo> groupTasks = findTasksByDialogueGroupId(dialogueGroupId);

            if (groupTasks.isEmpty()) {
                throw new ServiceException("未找到对话组ID为 " + dialogueGroupId + " 的任务");
            }

            // 检查所有任务是否都已完成
            validateTasksCompleted(groupTasks);

            // 构建视频片段列表
            List<Map<String, Object>> videoClips = buildVideoClips(groupTasks);

            log.info("准备进行云剪辑合成，使用的数字人视频素材:");
            for (int i = 0; i < videoClips.size(); i++) {
                Map<String, Object> clip = videoClips.get(i);
                log.info("  素材{}: 任务ID={}, 视频路径={}, 时长={}秒",
                    i+1, clip.get("taskId"), clip.get("videoUrl"), clip.get("duration"));
            }

            // 构建云剪辑Timeline
            String timeline = buildVideoClipTimeline(videoClips);
            log.info("云剪辑Timeline构建完成，包含{}个视频片段", videoClips.size());

            // 构建云剪辑请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("Title", StringUtils.isNotEmpty(title) ? title : "数字人对话视频_" + dialogueGroupId);
            params.put("Timeline", timeline);
            if (StringUtils.isNotEmpty(description)) {
                params.put("Description", description);
            }

            // 调用阿里云云剪辑API创建工程
            Map<String, Object> result = callIceCreateProject(params);

            // 提交导出任务
            if (result.containsKey("projectId")) {
                String projectId = result.get("projectId").toString();
                Map<String, Object> exportResult = submitExportJob(projectId);
                result.putAll(exportResult);
            }

            // 标记任务已进行云剪辑合成
            markTasksAsClipped(groupTasks, result);

            return result;
        } catch (Exception e) {
            log.error("根据对话组ID进行云剪辑合成失败: {}", dialogueGroupId, e);
            throw new ServiceException("云剪辑合成失败: " + e.getMessage());
        }
    }

    /**
     * 根据对话组ID查找任务
     */
    private List<PlatformVideo> findTasksByDialogueGroupId(String dialogueGroupId) {
        // 查询所有已完成的任务
        List<PlatformVideo> allCompletedTasks = platformVideoMapper.selectCompletedDialogueTasks();

        // 筛选出指定对话组的任务
        return allCompletedTasks.stream()
            .filter(task -> {
                String taskGroupId = extractDialogueGroupId(task);
                return dialogueGroupId.equals(taskGroupId);
            })
            .sorted((t1, t2) -> {
                Integer order1 = extractDialogueOrder(t1);
                Integer order2 = extractDialogueOrder(t2);
                // 处理null值，null值排在最后，如果都是null则按ID排序
                if (order1 == null && order2 == null) {
                    return t1.getId().compareTo(t2.getId());
                }
                if (order1 == null) return 1;
                if (order2 == null) return -1;
                return order1.compareTo(order2);
            })
            .collect(Collectors.toList());
    }

    /**
     * 验证任务是否都已完成
     */
    private void validateTasksCompleted(List<PlatformVideo> tasks) {
        for (PlatformVideo task : tasks) {
            if (!"3".equals(task.getStatus())) {
                throw new ServiceException("任务ID " + task.getId() + " 尚未完成，当前状态: " + task.getStatus());
            }
            if (StringUtils.isEmpty(task.getResultVideo())) {
                throw new ServiceException("任务ID " + task.getId() + " 的结果视频为空");
            }
        }
    }



    /**
     * 从任务中提取对话组ID
     */
    private String extractDialogueGroupId(PlatformVideo task) {
        try {
            if (StringUtils.isNotEmpty(task.getOperation())) {
                JSONObject operation = JSONObject.parseObject(task.getOperation());
                return operation.getString("dialogueGroupId");
            }
        } catch (Exception e) {
            log.warn("解析任务操作JSON失败，任务ID: {}", task.getId());
        }
        return null;
    }

    /**
     * 从任务中提取对话顺序
     */
    private Integer extractDialogueOrder(PlatformVideo task) {
        try {
            if (StringUtils.isNotEmpty(task.getOperation())) {
                JSONObject operation = JSONObject.parseObject(task.getOperation());
                Integer order = operation.getInteger("dialogueOrder");
                return order != null ? order : 0;
            }
        } catch (Exception e) {
            log.warn("解析任务对话顺序失败，任务ID: {}", task.getId());
        }
        return 0;
    }

    /**
     * 标记任务已进行云剪辑合成并保存合成结果
     */
    private void markTasksAsClipped(List<PlatformVideo> groupTasks, Map<String, Object> clipResult) {
        // 1. 更新原任务状态
        for (PlatformVideo task : groupTasks) {
            try {
                JSONObject operation = StringUtils.isNotEmpty(task.getOperation())
                    ? JSONObject.parseObject(task.getOperation())
                    : new JSONObject();

                operation.put("videoClipped", true);
                operation.put("clipJobId", clipResult.get("jobId"));
                operation.put("clipProjectId", clipResult.get("projectId"));
                operation.put("clipTime", new Date());

                task.setOperation(operation.toJSONString());
                platformVideoMapper.updatePlatformVideo(task);

            } catch (Exception e) {
                log.error("标记任务云剪辑状态失败，任务ID: {}", task.getId(), e);
            }
        }

        // 2. 创建云剪辑合成结果记录
        try {
            createClipResultRecord(groupTasks, clipResult);
        } catch (Exception e) {
            log.error("创建云剪辑合成结果记录失败", e);
        }
    }

    /**
     * 创建云剪辑合成结果记录
     */
    private void createClipResultRecord(List<PlatformVideo> groupTasks, Map<String, Object> clipResult) {
        if (groupTasks.isEmpty()) {
            return;
        }

        // 获取对话组ID
        String dialogueGroupId = extractDialogueGroupId(groupTasks.get(0));

        // 创建新的视频记录来保存合成结果
        PlatformVideo clipResultTask = new PlatformVideo();
        clipResultTask.setStatus("2"); // 处理中状态，等待云剪辑完成
        clipResultTask.setVersion("CLIP"); // 标记为云剪辑合成任务
        clipResultTask.setNumber("dialogue_clip_" + System.currentTimeMillis()); // 设置任务编号
        clipResultTask.setCreateBy(SecurityUtils.getUsername());
        clipResultTask.setUpdateBy(SecurityUtils.getUsername());
        clipResultTask.setCreatedAt(DateUtils.getNowDate());
        clipResultTask.setUpdatedAt(DateUtils.getNowDate());

        // 设置输出视频URL（从clipResult中获取）
        if (clipResult.containsKey("outputMediaURL")) {
            clipResultTask.setResultVideo(clipResult.get("outputMediaURL").toString());
        }

        // 构建operation信息
        JSONObject operation = new JSONObject();
        operation.put("task_type", "dialogue_video_clip");
        operation.put("dialogueGroupId", dialogueGroupId);
        operation.put("clipJobId", clipResult.get("jobId"));
        operation.put("clipProjectId", clipResult.get("projectId"));
        operation.put("sourceTaskCount", groupTasks.size());
        operation.put("clipStartTime", new Date());

        // 添加源任务信息
        JSONArray sourceTasks = new JSONArray();
        for (PlatformVideo task : groupTasks) {
            JSONObject sourceTask = new JSONObject();
            sourceTask.put("taskId", task.getId());
            sourceTask.put("videoUrl", task.getResultVideo());
            sourceTask.put("order", extractDialogueOrder(task));
            sourceTasks.add(sourceTask);
        }
        operation.put("sourceTasks", sourceTasks);

        clipResultTask.setOperation(operation.toJSONString());

        // 保存到数据库
        platformVideoMapper.insertPlatformVideo(clipResultTask);

        log.info("创建云剪辑合成结果记录成功，对话组ID: {}, 任务ID: {}, JobId: {}",
                dialogueGroupId, clipResultTask.getId(), clipResult.get("jobId"));
    }

    /**
     * 处理云剪辑任务完成回调
     * 当阿里云ICE完成视频合成后，更新数据库中的任务状态
     */
    public void handleClipJobCallback(String jobId, String status, String outputMediaURL) {
        try {
            log.info("处理云剪辑任务回调，JobId: {}, Status: {}, OutputURL: {}", jobId, status, outputMediaURL);

            // 查找对应的云剪辑任务
            List<PlatformVideo> clipTasks = platformVideoMapper.selectPlatformVideoList(new PlatformVideo());
            PlatformVideo clipTask = clipTasks.stream()
                .filter(task -> "CLIP".equals(task.getVersion()))
                .filter(task -> {
                    try {
                        JSONObject operation = JSONObject.parseObject(task.getOperation());
                        return jobId.equals(operation.getString("clipJobId"));
                    } catch (Exception e) {
                        return false;
                    }
                })
                .findFirst()
                .orElse(null);

            if (clipTask != null) {
                // 更新任务状态
                if ("Success".equalsIgnoreCase(status)) {
                    clipTask.setStatus("3"); // 成功
                    clipTask.setResultVideo(outputMediaURL); // 设置最终的视频URL
                    clipTask.setCompleteAt(DateUtils.getNowDate());
                } else {
                    clipTask.setStatus("4"); // 失败
                }

                // 更新operation信息
                JSONObject operation = StringUtils.isNotEmpty(clipTask.getOperation())
                    ? JSONObject.parseObject(clipTask.getOperation())
                    : new JSONObject();
                operation.put("clipCompleteTime", new Date());
                operation.put("clipStatus", status);
                operation.put("finalOutputURL", outputMediaURL);
                clipTask.setOperation(operation.toJSONString());

                // 保存到数据库
                platformVideoMapper.updatePlatformVideo(clipTask);

                log.info("云剪辑任务状态更新成功，TaskId: {}, Status: {}", clipTask.getId(), status);
            } else {
                log.warn("未找到对应的云剪辑任务，JobId: {}", jobId);
            }

        } catch (Exception e) {
            log.error("处理云剪辑任务回调失败，JobId: {}", jobId, e);
        }
    }

    /**
     * 查询阿里云ICE任务状态并更新数据库
     */
    public void checkAndUpdateIceJobStatus(String jobId) {
        try {
            log.info("开始查询阿里云ICE任务状态，JobId: {}", jobId);

            // 创建查询请求
            com.aliyun.ice20201109.models.GetMediaProducingJobRequest request =
                new com.aliyun.ice20201109.models.GetMediaProducingJobRequest();
            request.setJobId(jobId);

            // 调用阿里云ICE API查询任务状态
            com.aliyun.ice20201109.models.GetMediaProducingJobResponse response =
                iceClientAK.getMediaProducingJob(request);

            if (response != null && response.getBody() != null) {
                com.aliyun.ice20201109.models.GetMediaProducingJobResponseBody body = response.getBody();

                String status = body.getMediaProducingJob().getStatus();
                String outputMediaURL = null;

                // 如果任务成功，使用我们之前保存的输出URL
                if ("Success".equalsIgnoreCase(status)) {
                    // 输出URL在我们提交任务时已经设置，这里不需要重新获取
                    log.info("任务成功完成，将使用预设的输出URL");
                }

                log.info("阿里云ICE任务状态查询结果 - JobId: {}, Status: {}, OutputURL: {}",
                    jobId, status, outputMediaURL);

                // 更新数据库中的任务状态
                updateClipTaskStatus(jobId, status, outputMediaURL);

            } else {
                log.warn("查询阿里云ICE任务状态失败，响应为空，JobId: {}", jobId);
            }

        } catch (Exception e) {
            log.error("查询阿里云ICE任务状态失败，JobId: {}", jobId, e);
        }
    }

    /**
     * 更新云剪辑任务状态
     */
    private void updateClipTaskStatus(String jobId, String iceStatus, String outputMediaURL) {
        try {
            // 查找对应的云剪辑任务
            List<PlatformVideo> clipTasks = platformVideoMapper.selectPlatformVideoList(new PlatformVideo());
            PlatformVideo clipTask = clipTasks.stream()
                .filter(task -> "CLIP".equals(task.getVersion()))
                .filter(task -> {
                    try {
                        JSONObject operation = JSONObject.parseObject(task.getOperation());
                        return jobId.equals(operation.getString("clipJobId"));
                    } catch (Exception e) {
                        return false;
                    }
                })
                .findFirst()
                .orElse(null);

            if (clipTask != null) {
                boolean statusChanged = false;

                // 根据阿里云ICE状态更新本地状态
                if ("Success".equalsIgnoreCase(iceStatus)) {
                    clipTask.setStatus("3"); // 成功
                    if (StringUtils.isNotEmpty(outputMediaURL)) {
                        clipTask.setResultVideo(outputMediaURL);
                    }
                    clipTask.setCompleteAt(DateUtils.getNowDate());
                    statusChanged = true;
                    log.info("云剪辑任务完成，JobId: {}, OutputURL: {}", jobId, outputMediaURL);
                } else if ("Failed".equalsIgnoreCase(iceStatus)) {
                    clipTask.setStatus("4"); // 失败
                    clipTask.setCompleteAt(DateUtils.getNowDate());
                    statusChanged = true;
                    log.warn("云剪辑任务失败，JobId: {}", jobId);
                } else if ("Processing".equalsIgnoreCase(iceStatus) || "Init".equalsIgnoreCase(iceStatus)) {
                    // 仍在处理中，不需要更新
                    log.info("云剪辑任务仍在处理中，JobId: {}, Status: {}", jobId, iceStatus);
                }

                if (statusChanged) {
                    // 更新operation信息
                    JSONObject operation = StringUtils.isNotEmpty(clipTask.getOperation())
                        ? JSONObject.parseObject(clipTask.getOperation())
                        : new JSONObject();
                    operation.put("clipCompleteTime", new Date());
                    operation.put("iceStatus", iceStatus);
                    operation.put("finalOutputURL", outputMediaURL);
                    clipTask.setOperation(operation.toJSONString());

                    // 保存到数据库
                    platformVideoMapper.updatePlatformVideo(clipTask);

                    log.info("云剪辑任务状态更新成功，TaskId: {}, Status: {}", clipTask.getId(), iceStatus);
                }
            } else {
                log.warn("未找到对应的云剪辑任务，JobId: {}", jobId);
            }

        } catch (Exception e) {
            log.error("更新云剪辑任务状态失败，JobId: {}", jobId, e);
        }
    }

    /**
     * 查询云剪辑合成结果
     */
    public Map<String, Object> queryClipResult(String dialogueGroupId) {
        if (StringUtils.isEmpty(dialogueGroupId)) {
            throw new ServiceException("对话组ID不能为空");
        }

        try {
            // 查找对应的云剪辑任务
            List<PlatformVideo> clipTasks = platformVideoMapper.selectPlatformVideoList(new PlatformVideo());
            PlatformVideo clipTask = clipTasks.stream()
                .filter(task -> "CLIP".equals(task.getVersion()))
                .filter(task -> {
                    try {
                        JSONObject operation = JSONObject.parseObject(task.getOperation());
                        return dialogueGroupId.equals(operation.getString("dialogueGroupId"));
                    } catch (Exception e) {
                        return false;
                    }
                })
                .findFirst()
                .orElse(null);

            Map<String, Object> result = new HashMap<>();
            result.put("dialogueGroupId", dialogueGroupId);

            if (clipTask != null) {
                result.put("taskId", clipTask.getId());
                result.put("status", clipTask.getStatus());
                result.put("statusText", getStatusText(clipTask.getStatus()));
                result.put("createTime", clipTask.getCreatedAt());
                result.put("completeTime", clipTask.getCompleteAt());

                // 如果任务完成，提供视频URL
                if ("3".equals(clipTask.getStatus()) && StringUtils.isNotEmpty(clipTask.getResultVideo())) {
                    try {
                        String tempUrl = FileOperateUtils.getURL(clipTask.getResultVideo());
                        result.put("videoUrl", tempUrl);
                        result.put("originalPath", clipTask.getResultVideo());
                    } catch (Exception e) {
                        log.warn("生成临时访问URL失败: {}", e.getMessage());
                        result.put("videoUrl", clipTask.getResultVideo());
                    }
                }

                // 添加详细信息
                if (StringUtils.isNotEmpty(clipTask.getOperation())) {
                    try {
                        JSONObject operation = JSONObject.parseObject(clipTask.getOperation());
                        result.put("jobId", operation.getString("clipJobId"));
                        result.put("projectId", operation.getString("clipProjectId"));
                        result.put("sourceTaskCount", operation.getInteger("sourceTaskCount"));
                    } catch (Exception e) {
                        log.warn("解析operation信息失败: {}", e.getMessage());
                    }
                }

                result.put("message", "查询成功");
            } else {
                result.put("status", "NOT_FOUND");
                result.put("message", "未找到对应的云剪辑合成任务");
            }

            return result;

        } catch (Exception e) {
            log.error("查询云剪辑合成结果失败，对话组ID: {}", dialogueGroupId, e);
            throw new ServiceException("查询云剪辑合成结果失败: " + e.getMessage());
        }
    }

    /**
     * 获取状态文本描述
     */
    private String getStatusText(String status) {
        switch (status) {
            case "1": return "待处理";
            case "2": return "处理中";
            case "3": return "已完成";
            case "4": return "失败";
            default: return "未知状态";
        }
    }

    @Override
    public Map<String, Object> checkDialogueGroupStatus(String dialogueGroupId) {
        if (StringUtils.isEmpty(dialogueGroupId)) {
            throw new ServiceException("对话组ID不能为空");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("dialogueGroupId", dialogueGroupId);

        try {
            // 查找对话组的所有任务
            List<PlatformVideo> groupTasks = findTasksByDialogueGroupId(dialogueGroupId);

            if (groupTasks.isEmpty()) {
                result.put("status", "NOT_FOUND");
                result.put("message", "未找到对话组ID为 " + dialogueGroupId + " 的任务");
                result.put("canClip", false);
                return result;
            }

            // 统计任务状态
            int totalTasks = groupTasks.size();
            int completedTasks = 0;
            int failedTasks = 0;
            int processingTasks = 0;
            int pendingTasks = 0;

            List<Map<String, Object>> taskDetails = new ArrayList<>();

            for (PlatformVideo task : groupTasks) {
                Map<String, Object> taskInfo = new HashMap<>();
                taskInfo.put("taskId", task.getId());
                taskInfo.put("status", task.getStatus());
                taskInfo.put("order", extractDialogueOrder(task));

                // 从operation中提取更多信息
                if (StringUtils.isNotEmpty(task.getOperation())) {
                    try {
                        JSONObject operation = JSONObject.parseObject(task.getOperation());
                        taskInfo.put("speakerName", operation.getString("speakerName"));
                        taskInfo.put("text", operation.getString("text"));
                        taskInfo.put("videoClipped", operation.getBooleanValue("videoClipped"));
                    } catch (Exception e) {
                        log.warn("解析任务操作JSON失败，任务ID: {}", task.getId());
                    }
                }

                // 统计状态
                switch (task.getStatus()) {
                    case "3": // 完成
                        completedTasks++;
                        taskInfo.put("statusText", "已完成");
                        break;
                    case "4": // 失败
                        failedTasks++;
                        taskInfo.put("statusText", "失败");
                        break;
                    case "2": // 处理中
                        processingTasks++;
                        taskInfo.put("statusText", "处理中");
                        break;
                    case "1": // 待处理
                        pendingTasks++;
                        taskInfo.put("statusText", "待处理");
                        break;
                    default:
                        taskInfo.put("statusText", "未知状态");
                }

                taskDetails.add(taskInfo);
            }

            // 判断整体状态
            boolean allCompleted = completedTasks == totalTasks;
            boolean hasFailures = failedTasks > 0;
            boolean canClip = allCompleted && !hasFailures;

            String overallStatus;
            String message;

            if (allCompleted) {
                overallStatus = "COMPLETED";
                message = "所有任务已完成，可以进行云剪辑合成";
            } else if (hasFailures) {
                overallStatus = "FAILED";
                message = String.format("有 %d 个任务失败，无法进行云剪辑合成", failedTasks);
            } else if (processingTasks > 0) {
                overallStatus = "PROCESSING";
                message = String.format("有 %d 个任务正在处理中，请稍后再试", processingTasks);
            } else {
                overallStatus = "PENDING";
                message = String.format("有 %d 个任务待处理", pendingTasks);
            }

            // 检查是否已经进行过云剪辑
            boolean alreadyClipped = groupTasks.stream().anyMatch(task -> {
                try {
                    if (StringUtils.isNotEmpty(task.getOperation())) {
                        JSONObject operation = JSONObject.parseObject(task.getOperation());
                        return operation.getBooleanValue("videoClipped");
                    }
                } catch (Exception e) {
                    // 忽略解析错误
                }
                return false;
            });

            if (alreadyClipped) {
                canClip = false;
                message += "（已进行过云剪辑合成）";
            }

            result.put("status", overallStatus);
            result.put("message", message);
            result.put("canClip", canClip);
            result.put("totalTasks", totalTasks);
            result.put("completedTasks", completedTasks);
            result.put("failedTasks", failedTasks);
            result.put("processingTasks", processingTasks);
            result.put("pendingTasks", pendingTasks);
            result.put("alreadyClipped", alreadyClipped);
            result.put("taskDetails", taskDetails);

            return result;

        } catch (Exception e) {
            log.error("检查对话组状态失败: {}", dialogueGroupId, e);
            result.put("status", "ERROR");
            result.put("message", "检查状态失败: " + e.getMessage());
            result.put("canClip", false);
            return result;
        }
    }
}
